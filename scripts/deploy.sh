#!/bin/bash

# HTTPS OK 部署脚本

set -e

echo "开始部署 HTTPS OK 系统..."

# 检查系统要求
echo "检查系统要求..."

# 检查操作系统
if [[ "$OSTYPE" != "linux-gnu"* ]]; then
    echo "错误: 此脚本仅支持 Linux 系统"
    exit 1
fi

# 检查是否为 root 用户
if [[ $EUID -eq 0 ]]; then
    echo "警告: 不建议以 root 用户运行此脚本，但如果需要安装系统级依赖，可能需要sudo权限。"
fi

# 检查必要的命令
for cmd in curl wget git; do
    if ! command -v $cmd &> /dev/null; then
        echo "错误: 缺少必要的命令: $cmd"
        echo "请先安装: sudo apt-get update && sudo apt-get install -y $cmd"
        exit 1
    fi
done

# --- Go 环境安装 --- #
if ! command -v go &> /dev/null; then
    echo "Go 环境未安装，正在安装 Go 1.21.13..."
    GO_VERSION="1.21.13"
    GO_TAR="go${GO_VERSION}.linux-amd64.tar.gz"
    GO_URL="https://go.dev/dl/${GO_TAR}"

    wget -q $GO_URL -O /tmp/$GO_TAR
    sudo rm -rf /usr/local/go
    sudo tar -C /usr/local -xzf /tmp/$GO_TAR
    rm /tmp/$GO_TAR

    # 配置Go环境变量
    if ! grep -q "export PATH=\"/usr/local/go/bin:\$PATH\"" ~/.bashrc; then
        echo "export PATH=\"/usr/local/go/bin:\$PATH\"" >> ~/.bashrc
    fi
    # 立即生效环境变量
    export PATH="/usr/local/go/bin:$PATH"
    echo "Go ${GO_VERSION} 安装完成。"
else
    echo "Go 环境已安装: $(go version)"
fi

# 设置 Go 代理（使用国内镜像加速）
echo "设置 Go 代理..."
export GOPROXY=https://goproxy.cn,direct
export GOSUMDB=sum.golang.google.cn
echo "Go 代理设置完成。"

# --- MySQL 客户端安装 (如果需要) ---
# 如果系统上没有安装MySQL客户端，或者需要特定的版本，可以在这里添加安装逻辑
# 例如，对于Ubuntu/Debian:
# if ! command -v mysql &> /dev/null; then
#     echo "MySQL 客户端未安装，正在安装..."
#     sudo apt-get update
#     sudo apt-get install -y mysql-client
# fi

# 创建应用目录
APP_DIR="/opt/httpsok"
echo "创建应用目录: $APP_DIR"
sudo mkdir -p $APP_DIR
sudo chown $USER:$USER $APP_DIR

# 复制应用文件
echo "复制应用文件..."
# 确保当前目录是项目根目录
CURRENT_DIR=$(pwd)
if [[ "$(basename $CURRENT_DIR)" != "httpsok_improved" ]]; then
    echo "错误: 请在 \"httpsok_improved\" 目录下运行此脚本。"
    exit 1
fi

cp -r . $APP_DIR/
cd $APP_DIR

# 创建必要的目录
mkdir -p logs
mkdir -p configs

# 复制环境变量文件
if [[ ! -f .env ]]; then
    cp .env.example .env
    echo "已创建 .env 文件，请根据需要修改配置"
fi

# 设置权限
chmod +x bin/httpsok

# --- 数据库初始化和迁移 --- #
echo "正在执行数据库初始化和迁移..."
# 确保数据库服务已启动，并且.env中的DB配置正确
# 这里假设您已经手动创建了数据库和用户，或者数据库用户有创建数据库的权限
# 如果需要脚本自动创建数据库，请确保DB_USER有CREATE DATABASE权限

# 检查migrate工具是否安装，如果没有则安装
export PATH="$(go env GOPATH)/bin:$PATH" # 确保migrate命令在PATH中
if ! command -v migrate &> /dev/null; then
    echo "migrate 工具未安装，正在安装..."
    GO111MODULE=on go install -tags "mysql" github.com/golang-migrate/migrate/v4/cmd/migrate@latest
    echo "migrate 工具安装完成。"
fi

# 确保Go模块依赖已下载和整理
echo "正在下载和整理Go模块依赖..."
go mod download
go mod tidy
go mod vendor # 将依赖复制到vendor目录

# 尝试加载.env文件到当前shell环境
# 使用 set -a 和 set +a 来加载环境变量，并过滤掉注释行
set -a
if [ -f .env ]; then
    # 过滤掉注释行和空行，并确保每行都是 KEY=VALUE 格式
    while IFS=\= read -r key value; do
        if [[ ! -z "$key" && ! "$key" =~ ^# ]]; then
            export "$key"="$value"
        fi
    done < .env
fi
set +a

# 重新编译主程序，使用vendor模式
echo "重新编译主程序..."
go build -mod=vendor -o bin/httpsok ./cmd/main.go

# 构建数据库连接字符串
DB_URL="mysql://${DB_USER}:${DB_PASSWORD}@tcp(${DB_HOST}:${DB_PORT})/${DB_NAME}"

migrate -path=migrations -database "$DB_URL" up
echo "数据库迁移完成。"

# 安装 acme.sh（如果不存在）
if [[ ! -f /usr/local/bin/acme.sh ]]; then
    echo "安装 acme.sh..."
    curl https://get.acme.sh | sh -s email=<EMAIL>
    sudo ln -sf ~/.acme.sh/acme.sh /usr/local/bin/acme.sh
else
    echo "acme.sh 已安装。"
fi

# 创建 systemd 服务文件
echo "创建 systemd 服务..."
sudo tee /etc/systemd/system/httpsok.service > /dev/null <<EOF
[Unit]
Description=HTTPS OK Certificate Management System
After=network.target mysql.service # 添加mysql.service确保数据库先启动

[Service]
Type=simple
User=$USER
WorkingDirectory=$APP_DIR
ExecStart=$APP_DIR/bin/httpsok
Restart=always
RestartSec=5
Environment=PATH=/usr/local/go/bin:/usr/local/bin:/usr/bin:/bin
Environment=GOPATH=$(go env GOPATH)
Environment=GOROOT=$(go env GOROOT)

[Install]
WantedBy=multi-user.target
EOF

# 重新加载 systemd
sudo systemctl daemon-reload

# 启用服务
sudo systemctl enable httpsok

echo "部署完成！"
echo ""
echo "下一步操作："
echo "1. 编辑配置文件: nano $APP_DIR/.env"
echo "2. 启动服务: sudo systemctl start httpsok"
echo "3. 查看状态: sudo systemctl status httpsok"
echo "4. 查看日志: sudo journalctl -u httpsok -f"
echo ""
echo "Web 界面将在 http://localhost:8080 可用"
echo "默认管理员账号: admin / admin123"
