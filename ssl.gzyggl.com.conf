# httpsok系统复刻版 - 生产环境Nginx配置
# SSL证书自动化管理系统
# 域名: ssl.gzyggl.com
# 后端服务端口: 3001

# 限制请求频率配置
limit_req_zone $binary_remote_addr zone=api:10m rate=20r/s;
limit_req_zone $binary_remote_addr zone=auth:10m rate=5r/s;
limit_conn_zone $binary_remote_addr zone=conn_limit_per_ip:10m;

# 注释掉HTTP重定向，现在需要HTTP访问来申请证书和测试系统
# server {
#     listen 80;
#     listen [::]:80;
#     server_name ssl.gzyggl.com;
#
#     # 安全头部
#     add_header X-Frame-Options DENY always;
#     add_header X-Content-Type-Options nosniff always;
#     add_header X-XSS-Protection "1; mode=block" always;
#     add_header Referrer-Policy "strict-origin-when-cross-origin" always;
#
#     # Let's Encrypt验证路径
#     location /.well-known/acme-challenge/ {
#         root /var/www/html;
#         try_files $uri =404;
#     }
#
#     # 其他所有请求重定向到HTTPS
#     location / {
#         return 301 https://$server_name$request_uri;
#     }
# }

# HTTPS主配置 - 暂时注释SSL配置，等通过系统申请证书后再启用
# server {
#     listen 443 ssl http2;
#     listen [::]:443 ssl http2;
#     server_name ssl.gzyggl.com;
#
#     # SSL证书配置 - 将通过httpsok系统申请
#     # ssl_certificate /usr/local/ssl-cert-manager/certificates/ssl.gzyggl.com/fullchain.crt;
#     # ssl_certificate_key /usr/local/ssl-cert-manager/certificates/ssl.gzyggl.com/private.key;

# 临时HTTP配置，用于系统启动和证书申请
server {
    listen 80;
    listen [::]:80;
    server_name ssl.gzyggl.com;
    
    # 基本安全头部配置（HTTP模式）
    add_header X-Frame-Options DENY always;
    add_header X-Content-Type-Options nosniff always;
    add_header X-XSS-Protection "1; mode=block" always;
    add_header Referrer-Policy "strict-origin-when-cross-origin" always;
    
    # 隐藏Nginx版本
    server_tokens off;

    # 日志配置
    access_log /var/log/nginx/ssl.gzyggl.com.access.log;
    error_log /var/log/nginx/ssl.gzyggl.com.error.log;

    # 根目录配置
    root /usr/local/ssl-cert-manager/web;
    index index.html index.htm;

    # 连接限制
    limit_conn conn_limit_per_ip 20;

    # Let's Encrypt验证路径（用于证书申请）
    location /.well-known/acme-challenge/ {
        root /var/www/html;
        try_files $uri =404;
    }
    
    # 静态文件缓存
    location ~* \.(css|js|png|jpg|jpeg|gif|ico|svg|woff|woff2|ttf|eot)$ {
        expires 1y;
        add_header Cache-Control "public, immutable";
        add_header X-Content-Type-Options nosniff always;
    }
    
    # API代理配置
    location /api/ {
        # 代理到后端API服务（httpsok运行在3001端口）
        proxy_pass http://127.0.0.1:8080;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_set_header X-Forwarded-Host $host;
        proxy_set_header X-Forwarded-Port $server_port;
        
        # 超时配置
        proxy_connect_timeout 60s;
        proxy_send_timeout 60s;
        proxy_read_timeout 60s;

        # 限制请求频率
        limit_req zone=api burst=20 nodelay;

        # 缓存控制
        proxy_cache_bypass $http_upgrade;
        proxy_no_cache $http_upgrade;
        
        # 安全头部
        add_header X-Frame-Options DENY always;
        add_header X-Content-Type-Options nosniff always;
        add_header X-XSS-Protection "1; mode=block" always;
    }
    
    # 健康检查端点
    location /health {
        proxy_pass http://127.0.0.1:3001/api/v1/health;
        proxy_http_version 1.1;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        
        # 健康检查不记录访问日志
        access_log off;
    }
    
    # 管理界面
    location /admin/ {
        proxy_pass http://127.0.0.1:8080/api/v1/admin/;
        proxy_http_version 1.1;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        
        # 管理界面访问控制
        allow 127.0.0.1;
        allow 10.0.0.0/8;
        allow **********/12;
        allow ***********/16;
        deny all;
        
        # 基本认证
        auth_basic "httpsok System Admin";
        auth_basic_user_file /etc/nginx/.htpasswd;
    }
    
    # 认证相关端点（登录/注册）
    location ~ ^/api/v1/auth/ {
        proxy_pass http://127.0.0.1:8080;
        proxy_http_version 1.1;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;

        # 认证端点限制更严格
        limit_req zone=auth burst=5 nodelay;
        client_max_body_size 1M;
    }

    # 证书相关端点（需要认证）
    location ~ ^/api/v1/(certificates|servers|monitors)/ {
        proxy_pass http://127.0.0.1:3001;
        proxy_http_version 1.1;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;

        # 限制请求大小（证书文件可能较大）
        client_max_body_size 10M;

        # 限制请求频率
        limit_req zone=api burst=20 nodelay;

        # 超时配置（证书操作可能耗时较长）
        proxy_connect_timeout 120s;
        proxy_send_timeout 120s;
        proxy_read_timeout 120s;
    }
    
    # 默认页面
    location / {
        try_files $uri $uri/ /index.html;
        
        # 安全头部
        add_header X-Frame-Options DENY always;
        add_header X-Content-Type-Options nosniff always;
        add_header X-XSS-Protection "1; mode=block" always;
    }
    
    # 禁止访问隐藏文件
    location ~ /\. {
        deny all;
        access_log off;
        log_not_found off;
    }
    
    # 禁止访问备份文件
    location ~ ~$ {
        deny all;
        access_log off;
        log_not_found off;
    }
    
    # 错误页面
    error_page 404 /404.html;
    error_page 500 502 503 504 /50x.html;
    
    location = /404.html {
        root /usr/local/ssl-cert-manager/web;
        internal;
    }

    location = /50x.html {
        root /usr/local/ssl-cert-manager/web;
        internal;
    }
}
